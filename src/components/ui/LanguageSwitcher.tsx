import * as React from "react";
import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "./button";

interface LanguageSwitcherProps {
  className?: string;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ className }) => {
  const { changeLanguage, currentLanguage } = useTranslation();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
  ];

  const handleLanguageChange = (languageCode: string) => {
    changeLanguage(languageCode);
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      {languages.map((language) => (
        <Button
          key={language.code}
          variant={currentLanguage === language.code ? "default" : "outline"}
          size="sm"
          onClick={() => handleLanguageChange(language.code)}
          className="text-xs"
        >
          {language.name}
        </Button>
      ))}
    </div>
  );
};

export { LanguageSwitcher };
