import { truncateAddress, getFriendlyErrorMessage, formatNumberWithCommas, getExplorerUrl } from "@/components/ThinkTokenCard/TokenFunctions";
import Big from "big.js";
import { ThinkDialogButton, ThinkDialogContents } from "../ThinkDialog";


interface DialogProps {
    goldenTicketDetails: GoldenTicketDetails;
    onClose: () => void;
}

interface GoldenTicketDetails {
    totalGoldenTicketThink: string;
    txHash?: string | null;
}

export const GoldenTicketDialogContent: (obj: DialogProps) => ThinkDialogContents = ({
    onClose,
    goldenTicketDetails
}) => {

    const TitleComponent = () => null;

    const DescriptionComponent = () => {
        return (
            <div className="text-center text-lg">
                <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_golden_ticket.svg"></img>
                <p className="text-black text-2xl mb-2">
                    Golden Ticket Holder
                </p>
                <p className="font-mono text-[#99662F] uppercase">
                    Congratulations! You are a Golden Ticket Holder. Thanks for Grabbing a think agent bundle! You will receive an extra {formatNumberWithCommas(goldenTicketDetails.totalGoldenTicketThink)} $THINK.
                </p>
            </div>
        )
    };

    const FooterComponent = () => {
        return (
            <div className="flex flex-col space-y-6 w-full">
                <div className="flex flex-row space-x-2">
                    <ThinkDialogButton theme="secondary" onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                </div>
                {goldenTicketDetails.txHash && (
                    <p className="font-mono text-[#99662F] text-center uppercase">
                        View Transaction on <a href={`${getExplorerUrl()}/tx/${goldenTicketDetails.txHash}`} target="_blank" rel="noopener noreferrer" className="underline text-[#000000] hover:text-[#000000]">Etherscan</a>
                    </p>
                )}
            </div>
        );
    }


    return {
        TitleComponent,
        DescriptionComponent,
        FooterComponent,
    };
};
