import * as React from "react";
import { cn } from "@/lib/utils";
import { NavLink, useNavigate, useLocation } from "react-router";
import { MainNavComponentItem } from "../ui/MainNavComponentItem";
import { IconYoutube } from "../ui/IconYoutube";
import { IconX } from "../ui/IconX";
import { WalletButton } from "../ui/wallet-button";
import env from "@/environments/config";
import { Button } from "../ui/button";
import { CoundownTimer } from "../ui/CountdownTimer";
import { TGE_BONUS_ENDS, TGE_START } from "@/time-configs/TGE-configs";
import { IconChevronDown } from "../ui/IconChevronDown";
import { useAuth } from "../auth/AuthContext";
import { IconDiscord } from "../ui/IconDiscord";
import { IconMagicEden } from "../ui/IconMagicEden";

const Header = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"header">
>(({ className }, ref) => {
  const location = useLocation();
  const { user, authLogout } = useAuth();
  const pattern: RegExp = /Mobile|webOS/;
  const userAgent = window.navigator.userAgent
  let isMobileView = pattern.test(userAgent)
  if (window.innerWidth < 640) {
    isMobileView = true;
  }

  const [data, setData] = React.useState({
    showMobileNav: isMobileView,
    showDesktopNav: !isMobileView
  });
  const [toggleMenu, setToggleMenu] = React.useState(false);
  const [mobileDropDownOpen, setMobileDropDownOpen] = React.useState(false);
  const [openDropdown, setOpenDropdown] = React.useState<string[]>([]);

  const toggleDropdown = (dropdown: string) => {
    let newDropdowns = [...openDropdown];
    if (openDropdown.includes(dropdown)) {
      newDropdowns = openDropdown.filter(d => d !== dropdown);
    } else {
      newDropdowns = [...openDropdown, dropdown];
    }
    setOpenDropdown(newDropdowns); 
    if (newDropdowns.length === 0) {
      setMobileDropDownOpen(false);
    } else {
      setMobileDropDownOpen(true);
    }
  }

  function toggleMobileNav() {
    setToggleMenu(!toggleMenu);
  }

  function updateNav() {
    if (window.innerWidth < 640) {
      setData({ showMobileNav: true, showDesktopNav: false });
    } else {
      setData({ showMobileNav: false, showDesktopNav: true });
    }
  }

  React.useEffect(() => {
    window.addEventListener('resize', updateNav);

    return () => {
      window.removeEventListener('resize', updateNav);
    };
  }, []);

  let primaryButton = null;
  if (env.WALLET_LOGIN) {
    primaryButton = (
      <WalletButton variant="reverseGradient" loggedOutButton={(
        <Button
          onClick={() => window.open("https://x.com/thinkagents/status/1915475862579011954", '_blank')}
          variant="reverseGradient"
        >
          Mint Announcement
        </Button>
      )} />
    )
  } else {
    primaryButton = (
      <WalletButton variant="reverseGradient" loggedOutButton={(
        <Button
          onClick={() => {
            window.open("https://x.com/thinkagents", '_blank');
          }}
          variant="reverseGradient"
        >
          Join the Movement
        </Button>
      )} />
    );
  }

  const nftMintLink = "https://magiceden.us/launchpad/ethereum/think_agent_bundle";
  return (
    <header
      ref={ref}
      className={cn("Navigation w-full", className)}
      data-controller="navigation"
    >
      {/* Mobile Nav */}
      <div data-navigation-target="mobileNav" className={isMobileView ? '' : 'hidden'}>
        {
          env.WALLET_LOGIN && !location.pathname.includes('/dashboard') ?
            (
              <CoundownTimer
                title="Founder's Bonus Ends In"
                from={TGE_START}
                to={TGE_BONUS_ENDS}
                buttonLabel="Claim $THINK"
                action={() => window.location.href = "/#claim"}
              />
            ) : null
        }
        <div className="w-full h-2 Think-V2-Gradient"></div>
        <div data-controller="dropdown">
          <div className="flex flex-row justify-between items-center p-4">
            <NavLink onClick={() => setToggleMenu(false)} to="/" className="hover:text-red transition">
              <img src="/images/logo-think-horiz.svg" className="w-auto h-8 sm:h-8 md:h-11" />
            </NavLink>
            <div className="mt-2 hidden md:block">
              {primaryButton}
            </div>
            <button
              className="relative cursor-pointer group overflow-hidden mt-1 flex flex-row justify-center items-center transition-all"
              onClick={toggleMobileNav}
              id="user_dropdown_button"
              type="button">
              <div className="relative w-10 h-8">
                <span className="sr-only">Open main menu</span>
                <div className="absolute w-6 h-[2px] rounded-full bg-sand inset-1/2 -translate-x-1/2 translate-y-0"></div>
                <div className="absolute w-6 h-[2px] rounded-full bg-sand inset-1/2 -translate-x-1/2 translate-y-2"></div>
                <div className="absolute w-6 h-[2px] rounded-full bg-sand inset-1/2 -translate-x-1/2 -translate-y-2"></div>
              </div>
            </button>
          </div>

          {/* Mobile menu, show/hide based on menu open state. */}
          <div className={toggleMenu ? '' : 'hidden'} role="dialog" aria-modal="true"
            data-dropdown-target="menu"
            data-transition-enter-from="opacity-0 scale-95"
            data-transition-enter-to="opacity-100 scale-100"
            data-transition-leave-from="opacity-100 scale-100"
            data-transition-leave-to="opacity-0 scale-95">
            <div className="fixed inset-y-0 left-0 z-50 w-full h-2 Think-V2-Gradient"></div>
            <div className="fixed flex flex-col inset-y-0 left-0 z-50 w-full overflow-y-auto bg-black p-4 mt-2">
              <header className="flex items-center gap-x-6 w-full flex-row justify-between shrink-0 mb-6">

                <NavLink onClick={() => setToggleMenu(false)} to="/">
                  <img src="/images/logo-think-horiz.svg" className="w-auto h-8 sm:h-8 md:h-11" />
                </NavLink>
                <div className="mt-2 hidden md:block">
                  {primaryButton}
                </div>
                <button
                  className="ml-1 mr-1 mt-1 relative cursor-pointer group overflow-hidden flex flex-row justify-center items-center bg-black transition-all"
                  onClick={toggleMobileNav}
                  id="user_dropdown_button"
                  type="button">
                  <div className="-m-2.5 p-2.5">
                    <span className="sr-only">Close menu</span>
                    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" strokeWidth="2" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                </button>
              </header>
              <div className="flex flex-col justify-between items-start flex-grow">
                <section className={`st-accordion font-heading text-offWhite ${mobileDropDownOpen ? 'dropdown-active' : ''}`} data-controller="accordion">
                  <style>
                  </style>
                  <div className="flex flex-col gap-y-4 text-2xl">
                    {/* About Dropdown */}
                    <div id="about-dropdown-container" className={`w-full  dropdown-item ${openDropdown.includes('about') ? 'dropdown-item-active' : ''}`}>
                      <button 
                        onClick={() => {
                          toggleDropdown('about')
                        }}
                        className="flex w-full items-center justify-left text-2xl text-white hover:text-teal transition"
                      >
                        <span>About</span>
                        <IconChevronDown className="ml-4 w-5 h-5" />
                      </button>
                      <div id="about-dropdown" className={`${openDropdown.includes('about') ? '' : 'hidden'} pl-4 mt-2 flex flex-col gap-y-3`}>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/about" title="Team">Team</MainNavComponentItem>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/partners" title="Partners">Partners</MainNavComponentItem>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/investors" title="Investors">Investors</MainNavComponentItem>
                      </div>
                    </div>
                    
                    {/* THINK Protocol Dropdown (was Products) */}
                    <div id="products-dropdown-container" className={`w-full dropdown-item ${openDropdown.includes('products') ? 'dropdown-item-active' : ''}`}>
                      <button
                        onClick={() => {
                          toggleDropdown('products')
                        }}
                        className={`flex w-full items-center justify-left text-2xl ${openDropdown.includes('products') ? 'text-teal' : 'text-white'} hover:text-teal transition`}
                      >
                        THINK Protocol
                        <IconChevronDown className={`ml-4 w-5 h-5 transition-transform duration-200 ${openDropdown.includes('products') ? 'rotate-180' : ''}`} />
                      </button>
                      {openDropdown.includes('products') && (
                        <div className="pl-4 mt-2 flex flex-col gap-y-3">
                          {/* <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/" title="THINK Agents">THINK Agents</MainNavComponentItem> */}
                          <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/claim" title="THINK Token">THINK Token</MainNavComponentItem>
                          <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/products/thinkubator" title="THINK Builders">THINK Builders</MainNavComponentItem>
                        </div>
                      )}
                    </div>
                    
                    {/* Resources Dropdown */}
                    <div id="resources-dropdown-container" className={`w-full mt-2 dropdown-item ${openDropdown.includes('resources') ? 'dropdown-item-active' : ''}`}>
                      <button 
                        onClick={() => {
                          toggleDropdown('resources')
                        }}
                        className="flex w-full items-center justify-left text-2xl text-white hover:text-teal transition"
                      >
                        <span>Resources</span>
                        <IconChevronDown className="ml-4 w-5 h-5" />
                      </button>
                      <div id="resources-dropdown" className={`${openDropdown.includes('resources') ? '' : 'hidden'} pl-4 mt-2 flex flex-col gap-y-3`}>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="https://docs.thinkagents.ai" target="_blank" title="Docs">Docs</MainNavComponentItem>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="https://docs.thinkagents.ai/whitepaper" target="_blank" title="Whitepaper">Whitepaper</MainNavComponentItem>
                        <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/faq" title="FAQs">FAQs</MainNavComponentItem>
                      </div>
                    </div>
                    
                    {/* Contact */}
                    <MainNavComponentItem onClick={() => setToggleMenu(false)} to="/contact" title="Contact" className=" dropdown-item">Contact</MainNavComponentItem>
                  </div>
                </section>
                <div className="absolute bottom-0 left-0 w-full flex flex-col items-start pb-8 pl-6 bg-black">
                  <NavLink to="/privacy-policy" onClick={() => setToggleMenu(false)} title="Your Privacy" className="hover:text-teal transition text-xs font-mono mb-1">Your Privacy</NavLink>
                  <NavLink to="/terms-and-conditions" onClick={() => setToggleMenu(false)} title="Terms and Conditions" className="hover:text-teal transition text-xs font-mono mb-2">Terms and Conditions</NavLink>
                  <div className="flex flex-row gap-6 justify-start items-center mt-2">
                    <NavLink to="https://x.com/thinkagents" title="X (Twitter)" target="_blank" className="text-off-white hover:text-teal">
                      <IconX className="h-8" />
                    </NavLink>
                    <NavLink to="https://discord.gg/EjRsRrVc5F" title="Discord" target="_blank" className="text-off-white hover:text-teal">
                      <IconDiscord className="h-8" />
                    </NavLink>
                    <NavLink to="https://www.youtube.com/@ThinkAgents" title="YouTube" target="_blank" className="text-off-white hover:text-teal">
                      <IconYoutube className="h-8" />
                    </NavLink>
                    <NavLink to="https://magiceden.us/collections/ethereum/******************************************" title="Magic Eden" target="_blank" className="text-off-white hover:text-teal">
                      <IconMagicEden className="h-8" />
                    </NavLink>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Nav */}
      <div data-navigation-target="desktopNav" className={isMobileView ? 'hidden' : ''}>
        {
          env.WALLET_LOGIN && !location.pathname.includes('/dashboard') ?
            (
              <CoundownTimer
                title="Founder's Bonus Ends In"
                from={TGE_START}
                to={TGE_BONUS_ENDS}
                buttonLabel="Claim $THINK"
                action={() => window.location.href = "/#claim"}
              />
            ) : null
        }
        <div className="w-full h-2 Think-V2-Gradient"></div>
        <div className="w-full px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 py-2 lg:py-4">
          <div className="mx-auto flex w-full items-center justify-between gap-4 md:gap-4 lg:gap-6 xl:gap-12">
            <NavLink to="/" className="block transition hover:bg-gradient-to-b bg-clip-text hover:from-teal hover:to-sand hover:text-black hover:animate-logoGlow hover:animate-pulse hover:animate-infinite mask-logo">
              <h1 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">Think</h1>
            </NavLink>
            <nav data-controller="menu" className="hidden lg:flex justify-items-start place-content-start text-offWhite gap-x-4 md:gap-x-6 lg:gap-x-8 md:text-xs lg:text-sm md:font-semibold md:leading-6 grow justify-center">
              {/* About Dropdown */}
              <div className="flex relative group">
                <button
                  className={`flex items-center gap-1 transition py-2 ${openDropdown.includes('about') ? 'text-teal underline' : 'text-white group-hover:text-teal'}`}
                  onClick={() => toggleDropdown('about')}
                  onMouseEnter={() => setOpenDropdown(['about'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  About
                  <IconChevronDown className={`w-4 transition-transform ${openDropdown.includes('about') ? 'rotate-180 text-teal' : 'group-hover:rotate-180'}`} />
                </button>
                <div
                  className={`absolute left-0 top-full mt-1 w-48 bg-black border border-gray-800 rounded-md shadow-lg z-50 flex flex-col dropdown-item ${openDropdown.includes('about') ? 'dropdown-active' : 'opacity-0 invisible'} transition-all`}
                  onMouseEnter={() => setOpenDropdown(['about'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  <MainNavComponentItem isChild={true} to="/about" title="Team" className={`p-6 hover:bg-gray-900 text-white ${openDropdown.includes('about') ? 'dropdown-item-active' : ''}`}>Team</MainNavComponentItem>
                  <MainNavComponentItem isChild={true} to="/partners" title="Partners" className={`p-6 hover:bg-gray-900 ${openDropdown.includes('about') ? 'dropdown-item-active' : ''}`}>Partners</MainNavComponentItem>
                  <MainNavComponentItem isChild={true} to="/investors" title="Investors" className={`p-6 hover:bg-gray-900 ${openDropdown.includes('about') ? 'dropdown-item-active' : ''}`}>Investors</MainNavComponentItem>
                </div>
              </div>
              
              {/* THINK Protocol Dropdown (was Products) */}
              <div className="flex relative group">
                <button
                  className={`flex items-center gap-1 transition py-2 ${openDropdown.includes('products') ? 'text-teal underline' : 'text-white group-hover:text-teal'}`}
                  onClick={() => toggleDropdown('products')}
                  onMouseEnter={() => setOpenDropdown(['products'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  THINK Protocol
                  <IconChevronDown className={`w-4 transition-transform ${openDropdown.includes('products')  ? 'rotate-180 text-teal' : 'group-hover:rotate-180'}`} />
                </button>
                <div
                  className={`absolute left-0 top-full mt-1 w-48 bg-black border border-gray-800 rounded-md shadow-lg z-50 flex flex-col dropdown-item ${openDropdown.includes('products') ? 'dropdown-active' : 'opacity-0 invisible'} transition-all`}
                  onMouseEnter={() => setOpenDropdown(['products'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  {/* <MainNavComponentItem isChild={true} to="/" title="THINK Agents" className={`p-6 hover:bg-gray-900 ${openDropdown === 'products' ? 'dropdown-item-active' : ''}`}>THINK Agents</MainNavComponentItem> */}
                  <MainNavComponentItem isChild={true} to="/claim" title="THINK Token" className={`p-6 hover:bg-gray-900 ${openDropdown.includes('products') ? 'dropdown-item-active' : ''}`}>THINK Token</MainNavComponentItem>
                  <MainNavComponentItem isChild={true} to="/products/thinkubator" title="THINK Builders" className={`p-6 hover:bg-gray-900 ${openDropdown.includes('products') ? 'dropdown-item-active' : ''}`}>THINK Builders</MainNavComponentItem>
                </div>
              </div>
              
              {/* Resources Dropdown */}
              <div className="flex relative group">
                <button
                  className={`flex items-center gap-1 transition py-2 ${openDropdown.includes('resources') ? 'text-teal underline' : 'text-white group-hover:text-teal'}`}
                  onClick={() => toggleDropdown('resources')}
                  onMouseEnter={() => setOpenDropdown(['resources'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  Resources
                  <IconChevronDown className={`w-4 transition-transform ${openDropdown.includes('resources') ? 'rotate-180 text-teal' : 'group-hover:rotate-180'}`} />
                </button>
                <div
                  className={`absolute left-0 top-full mt-1 w-48 bg-black border border-gray-800 rounded-md shadow-lg z-50 flex flex-col dropdown-item ${openDropdown.includes('resources') ? 'dropdown-active' : 'opacity-0 invisible'} transition-all`}
                  onMouseEnter={() => setOpenDropdown(['resources'])}
                  onMouseLeave={() => setOpenDropdown([])}
                >
                  <MainNavComponentItem isChild={true} to="https://docs.thinkagents.ai" target="_blank" title="Docs" className={`p-6 hover:bg-gray-900 text-white ${openDropdown.includes('resources') ? 'dropdown-item-active' : ''}`}>Docs</MainNavComponentItem>
                  <MainNavComponentItem isChild={true} to="https://docs.thinkagents.ai/whitepaper" target="_blank" title="Whitepaper" className={`p-6 hover:bg-gray-900 ${openDropdown .includes('resources') ? 'dropdown-item-active' : ''}`}>Whitepaper</MainNavComponentItem>
                  <MainNavComponentItem isChild={true} to="/faq" title="FAQs" className={`p-6 hover:bg-gray-900 ${openDropdown.includes('resources') ? 'dropdown-item-active' : ''}`}>FAQs</MainNavComponentItem>
                </div>
              </div>
              
              {/* Contact */}
              <MainNavComponentItem to="/contact" title="Contact">Contact</MainNavComponentItem>
            </nav>
            <div className="hidden lg:block">
              {primaryButton}
            </div>
          </div>
        </div>
        <div id="navigation_groups" className="w-full absolute z-30 bg-black">
        </div>
        <div id="nav-scrim" className="absolute w-full h-full bg-black-950/50 backdrop-blur-sm z-20 hidden"></div>
      </div>
    </header>
  );
});
Header.displayName = "Header";

export { Header };
