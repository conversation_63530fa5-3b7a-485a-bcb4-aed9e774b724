import * as React from "react";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router";
import { MainNavComponentItem } from "../ui/MainNavComponentItem";
import { PrimaryContainerComponent } from "./PrimaryContainerComponent";
import { IconYoutube } from "../ui/IconYoutube";
import { IconX } from "../ui/IconX";
import { IconDiscord } from "../ui/IconDiscord";
import { IconMagicEden } from "../ui/IconMagicEden";

const Footer = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<"footer">
>(({ className }, ref) => {
    const location = useLocation();
    if (location.pathname.indexOf('/dashboard') !== -1) {
        return null; // Render nothing for the specified routes
    }

    return (
        <footer
            ref={ref}
            className={cn("Footer w-full flex flex-col justify-center items-center shrink-0", className)}
        >
            <PrimaryContainerComponent className="w-full flex flex-col md:flex-row md:justify-between py-20">
                <div className="flex flex-row mb-8">
                    <img src="/images/token-brain-fist.svg" className="text-black w-12 md:w-32 h-auto lg:w-48" />
                </div>
                <div className="text-off-white">
                    <div className="flex flex-col lg:grid lg:grid-cols-2 xl:grid-cols-2 grid-rows-2 gap-x-8 gap-y-4 font-semibold text-xl md:mt-4 md:text-lg lg:text-xl mb-12">
                        <NavLink to="/about" className="hover:text-teal order-first" title="About">About</NavLink>
                        <NavLink to="/investors" className={"hover:text-teal"} title="Investors">Investors</NavLink>
                        <NavLink to="/partners" className={"hover:text-teal"} title="Partners">Partners</NavLink>
                        {/* <NavLink to="/blog" className={"hover:text-slate-500"} title="News">News</NavLink> */}
                        <NavLink to="https://docs.thinkagents.ai" className={"hover:text-teal"} target="_blank" title="Docs">Docs</NavLink>
                        <NavLink to="/contact" className={"hover:text-teal md:order-last"} title="Contact">Contact</NavLink>
                    </div>
                </div>
            </PrimaryContainerComponent>
            <PrimaryContainerComponent className="w-full flex flex-col md:flex-row items-start md:items-center md:justify-between">
                <div className="text-2xs text-slate-200 font-mono flex flex-col md:flex-row gap-1 md:gap-6">
                    <NavLink to="/privacy-policy" title="Your Privacy" className="hover:text-teal">Your Privacy</NavLink>
                    <NavLink to="/terms-and-conditions" title="Terms and Conditions" className="hover:text-teal">Terms and Conditions</NavLink>
                </div>
                <div className="flex flex-row gap-4 font-semibold text-xl mt-4 md:mt-0 md:text-lg lg:text-xl mb-4 text-off-white">
                    <NavLink to="https://x.com/thinkagents" title="Your Privacy" target="_blank" className="text-off-white hover:text-teal">
                        <IconX className="h-8" />
                    </NavLink>
                    <NavLink to="https://discord.gg/EjRsRrVc5F" title="Join our Discord" target="_blank" className="text-off-white hover:text-teal">
                        <IconDiscord className="h-8" />
                    </NavLink>
                    <NavLink to="https://www.youtube.com/@ThinkAgents" title="Your Privacy" target="_blank" className="text-off-white hover:text-teal">
                        <IconYoutube className="h-8" />
                    </NavLink>
                    <NavLink to="https://magiceden.us/collections/ethereum/******************************************" title="Magic Eden Collection" target="_blank" className="text-off-white hover:text-teal">
                        <IconMagicEden className="h-8" />
                    </NavLink>
                </div>
            </PrimaryContainerComponent>
            <div className="w-full mt-4 linear-gradient-teal-to-sand h-[5px]"></div>
        </footer>

    );
});
Footer.displayName = "Footer";

export { Footer };