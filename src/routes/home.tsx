import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import React from "react";
import YouTube from "react-youtube";
import { Options, YouTubePlayer } from 'youtube-player/dist/types';
import ThinkTokenCard from '@/components/ThinkTokenCard/ThinkTokenCard';
import { useAuth } from '@/components/auth/AuthContext';
import env from "@/environments/config";
import { ThinkProvider } from "@/components/think-provider/ThinkProvider";

export default function Home() {
    const [showVideo, setShowVideo] = React.useState(false);
    const [videoPlayer, setVideoPlayer] = React.useState<YouTubePlayer | null>(null);
    // const isLoggedIn = true;
    const { user } = useAuth();
    const tokenBurnEnabled = env.TOKEN_BURN_ENABLED;

    // console.log("user", user);

    function toggleVideo() {
        const newShowVideo = !showVideo
        setShowVideo(newShowVideo);
        console.log(newShowVideo)
        console.log(videoPlayer)
        if (videoPlayer !== null) {
            if (newShowVideo) {
                videoPlayer.playVideo();
            } else {
                videoPlayer.pauseVideo();
            }
        }
    }
    function onReady(event: any) {
        setVideoPlayer(event.target);
        event.target.pauseVideo();
    }

    function onPuase() {
        toggleVideo();
    }

    const options = {
        playerVars: {
            autoplay: 0,
            controls: 1,
            enablejsapi: 1,
            rel: 0
        },
    } as Options;

    return (
        <div>
            <header className="relative flex flex-col justify-between items-center w-full h-full">
                <div className="relative w-full flex flex-col justify-center items-center p-6 overflow-x-clip">
                    <video autoPlay={true} loop muted playsInline={true} className="absolute w-full left-[55.6875%] top-[74%] lg:left-[49.5%] lg:top-[74%] -translate-x-1/4 -translate-y-1/2 scale-[2.5] lg:scale-[2] z-[-1] opacity-45">
                        <source src="videos/hands-sphere-hologram.mp4" type="video/mp4" />
                    </video>
                    <div className="flex justify-center">
                        <img src="/images/logos/knocked_out_coin_w_rays.svg" className="block w-[37.5vw] lg:w-[30vw]" />
                    </div>
                </div>

                <h1 className="heading-display text-off-white text-center mb-3 max-w-sm md:max-w-lg lg:max-w-3xl xl:max-w-5xl">AI You Own</h1>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    THINK is the open protocol for AI agents you can own, evolve, and personalize.
                </p>
            </header>

            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">



                {/* Video with Gradient border */}
                <div className="mt-12 mb-12 w-full flex flex-col justify-center items-center" id="about">
                    <div className="w-full linear-gradient-sand-to-teal h-[5px]"></div>
                    <div className="w-full flex flex-row items-stretch">
                        <div className="vertical-gradient-sand-to-teal w-[5px]"></div>
                        <div id="video-container" className="w-[calc(100%-10px)] h-full relative">
                            <img src="/images/Think_Social+YT.png" id='video-thumbnail' style={showVideo ? { display: 'none' } : {}} className="w-full h-full absolute object-cover z-10" />
                            <button id="play-button"
                                style={showVideo ? { display: 'none' } : {}}
                                className="absolute group inset-0 z-20 flex items-center justify-center my-auto transition-colors duration-300 ease-in-out hover:bg-teal/20" onClick={toggleVideo}>
                                <img src="/images/homepage/play-button.svg" id='video-thumbnail' className="'w-1/3 h-1/3  transition-colors duration-300 ease-in-out group-hover:text-teal/20" />
                            </button>

                            <YouTube
                                style={showVideo ? { zIndex: 10 } : {}}
                                iframeClassName="w-full h-full aspect-[16/9] inset-0 z-[-1]"
                                videoId="jHOkU6GdRGg"
                                opts={options}
                                onReady={onReady}
                                onPause={onPuase}
                                id="video"
                            />

                        </div>
                        <div className="vertical-gradient-teal-to-sand w-[5px]"></div>
                    </div>
                    <div className="w-full linear-gradient-teal-to-sand h-[5px]"></div>
                </div>

                <h1 className="pt-4 uppercase text-4xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    Connect to open-source agents, tools, and MCPs into one composable framework.
                </h1>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    THINK connects AI agents to a growing network of open-source tools, models, and protocols—making them smarter, more interoperable, and fully user-owned. Built for developers, creators, and communities, THINK is the foundation of a new agent-powered internet—where intelligence is composable, data stays with the user, and innovation is permissionless. Whether you're building a single agent or an entire ecosystem, THINK is the protocol that makes it all work—together
                    We the people must choose AI's path. We must ensure it never becomes a tool used by the few to manipulate, control, and ultimately enslave the minds of many. Decentralization is our shield against this tyranny—it's how we safeguard free thought and prevent the rise of digital tyrants.
                </p>


                <div className="relative w-full pb-24 min-h-[500px] flex flex-col justify-center items-center overflow-visible">

                    <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                        <header className="flex flex-row justify-center items-center gap-4 ">
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                            <h2 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">Think&nbsp;Benefits</h2>
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        </header>
                    </div>

                    <div className="mt-12 md:px-4 lg:px-10 w-full justify-center items-stretch grid grid-cols-1 lg:grid-cols-2 gap-4">

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">Professional Agents</h3>
                            <p className="font-mono text-black-400">Fuel adaptive AI entities that interoperate across platforms, acquiring new skills and attributes wherever they go.</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">Own Your Experience</h3>
                            <p className="font-mono text-black-400">From agent creation to in-game transactions, $THINK gives you real control over how agents evolve and interact.</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">Patent Protection From Trolls</h3>
                            <p className="font-mono text-black-400">Innovate confidently under $THINK’s patent umbrella, shielded from malicious infringement claims so you can focus on building.</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">Seamless Interoperability</h3>
                            <p className="font-mono text-black-400">Agents carry their personality, data, environmental awareness and assets across ecosystems, fostering fluid collaboration and persistent identities.</p>
                        </div>

                    </div>
                </div>
            </PrimaryContainerComponent>


            <div className="w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}