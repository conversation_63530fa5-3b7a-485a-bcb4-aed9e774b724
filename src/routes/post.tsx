import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { Post as PostType } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { NavLink, useNavigate, useParams } from "react-router";
import { format } from "date-fns";
import env from "@/environments/config";
import { Loading } from "@/components/ui/loading";

export default function Post() {
    const navigate = useNavigate();
    const { slug } = useParams();
    if (!slug) {
        navigate('/404');
    }
    const query = useQuery({
        queryKey: ["post"],
        queryFn: async () => {
            const data = await apiClient.getBlogPost(slug!);
            return data;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });


    if (query && query.isLoading) {
        return <Loading />;
    } else if (query && query.isError) {
        navigate('/404');
    }
    console.log(query)
    const post: PostType = query.data;
    const formattedDate = format(post.created_at, 'MMMM d, yyyy');
    const image = post.image ? post.image : null;
    return (
        <div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">
                <h1 className="pt-16 uppercase text-6xl font-bold text-left text-off-white font-token w-full">
                    {post.title}
                </h1>
                <div className="text-left w-full">
                    <span className="font-mono text-gray-400 text-xs lg:text-sm font-bold mb-4 text-left mr-6">{formattedDate}</span>
                    <span className="font-mono text-gray-600 text-xs lg:text-sm font-bold mb-4 text-left mr-6">Team Think</span>
                </div>
                <div className="mt-16 relative bg-white w-full pb-[50%] z-0">
                    {image && <img src={`${env.API_BASE_URL}${image}`} className="absolute block w-full h-full inset-0 object-fill z-10" />}
                </div>


                <div className="mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl pt-16 text-white mb-4 text-lg text-left w-full" dangerouslySetInnerHTML={{ __html: post.body || "" }}>
                </div>
            </PrimaryContainerComponent>
            <div className="mt-16 w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}