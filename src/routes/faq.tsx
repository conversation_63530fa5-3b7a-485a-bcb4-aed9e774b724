import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { FAQItem } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { NavLink } from "react-router";

export default function FAQ() {
    const [openedIds, setOpenedIds] = useState<number[]>([]);

    function toggleOpened(event: any, id: number) {
        event.preventDefault();
        if (openedIds.includes(id)) {
            setOpenedIds(openedIds.filter((i) => i !== id));
        } else {
            setOpenedIds([...openedIds, id]);
        }
        console.log(openedIds)
    }

    const query = useQuery({
        queryKey: ["faqs"],
        queryFn: async () => {
            const data = await apiClient.getFAQ();
            if (data && data['faqs']) {
                data['faqs'].forEach((faq: FAQItem) => {
                    if (faq.is_open) {
                        setOpenedIds([...openedIds, faq.id]);
                    }
                });
            }
            return data;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });
    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">FAQ</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">
                <section id="faq" className="my-12">
                    <div className="st-accordion" data-controller="accordion">
                        {query && query.data && query.data['faqs'] && query.data['faqs'].map((faq: FAQItem) => {
                            const isOpen = openedIds.includes(faq.id);
                            return (
                                <div>
                                    <div>
                                        <a href="#" className={`st-accordion__icon flex flex-row justify-between py-6  ${isOpen ? 'st-accordion__icon--opened' : ''}`} onClick={(e) => toggleOpened(e, faq.id)}>
                                            <div className="font-token text-off-white text-2xl flex-grow">{faq.question}</div>
                                            <span className="flex flex-direction row justify-end items-start w-10 flex-shrink">
                                                <IconChevronDown className="w-6 text-off-white" />
                                            </span>
                                        </a>
                                    </div>
                                    <div className={`st-accordion__content border-b border-black-700 font-mono text-off-white ${isOpen ? 'st-accordion__content--visible' : ''}`}>
                                        <div className="py-6 px-2" dangerouslySetInnerHTML={{ __html: faq.answer || "" }}>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </section>

            </PrimaryContainerComponent>
            <div className="mt-16 w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}