import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import env from "@/environments/config";
import { useState } from "react";
import { apiClient } from "@/lib/api";

export default function Contact() {
    const [formData, setFormData] = useState({
        full_name: '',
        email: '',
        twitter: '',
        message: '',
    });
    const [notice, setNotice] = useState<string |null>(null);
    const [error, setError] = useState<string |null>(null);

    const handleChange = (e: any) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async (e: any) => {
        e.preventDefault();

        try {
            const response = await apiClient.submitContact(formData);
            console.log(response);
            setNotice(response["notice"]);
            setError(null);
            return;
        } catch (error: any) {
            let errorJson = null;
            console.log(error.message);
            try {
                errorJson = JSON.parse(error.message);
            } catch (e) {
                console.error(e);
            }
            console.log(errorJson);
            if (errorJson && errorJson["alert"]) {
                setError(errorJson["alert"]);
                setNotice(null);
            } else {
                setError("Sorry we received an error. Please try again later. If you continue to have issues, please send a messaage to us on Twitter.");
                setNotice(null);
            }

        }
    };

    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">CONTACT</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">
                <p className="font-mono text-gray-400 pb-4">
                    Thanks for your interest in building with $THINK. Please tell us about your project below:
                </p>

                { notice && <div className="mt-5 bg-teal/20 text-teal px-4 py-3 rounded mb-4">{notice}</div>}

                { error && <div className="mt-5 bg-red/20 text-red px-4 py-3 rounded mb-4">{error}</div>}

                <form onSubmit={handleSubmit} className="w-full flex flex-col gap-4 mt-8" accept-charset="UTF-8">
                    <div className="flex flex-col gap-4">
                        <input onChange={handleChange} type="text" name="full_name" id="full_name" className="w-full bg-black-700 text-off-white px-4 py-3 border-2 rounded focus:outline-none focus:ring-0 focus:border-teal focus:border-2 placeholder:text-black-400" placeholder="Full Name" required={true} />

                        <input onChange={handleChange} type="email" name="email" id="email" className="w-full bg-black-700 text-off-white px-4 py-3 border-2 rounded focus:outline-none focus:ring-0 focus:border-teal focus:border-2 placeholder:text-black-400" placeholder="Email" required={true}   />

                        <input onChange={handleChange} type="text" name="twitter" id="twitter" className="w-full bg-black-700 text-off-white px-4 py-3 border-2 rounded focus:outline-none focus:ring-0 focus:border-teal focus:border-2 placeholder:text-black-400" placeholder="Twitter Handle" />

                        <textarea onChange={handleChange} name="message" id="message" className="w-full bg-black-700 text-off-white px-4 py-3 border-2 rounded focus:outline-none focus:ring-0 focus:border-teal focus:border-2 placeholder:text-black-400" placeholder="Message" rows={5} required={true} ></textarea>
                    </div>

                    <input type="submit" name="commit" value="Continue" className="w-full bg-teal text-black font-bold py-4 px-6 rounded hover:bg-gradient-to-b hover:from-teal hover:to-sand disabled:bg-black-500 disabled:hover:bg-black-500 transition-colors cursor-pointer" data-disable-with="Submitting..." />
                </form>
            </PrimaryContainerComponent>
            <div className="mt-16 w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}