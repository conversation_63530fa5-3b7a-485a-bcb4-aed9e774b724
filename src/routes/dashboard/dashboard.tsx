import { useAuth, USER_SIGNED_MESSAGE_KEY, USER_WALLET_KEY } from "@/components/auth/AuthContext";
import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { useNavigate } from "react-router";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiClient, isGetTokenBalanceResponse, isGetWalletResponse, web3Client } from "@/lib/api";
import { ActiveStake, StakeProvider, useStake } from "@/components/think-provider/StakeProvider";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Butt<PERSON>, Gray<PERSON><PERSON><PERSON><PERSON>uttonIneligible } from "@/components/ui/button";
import { PinWheel } from "@/components/ui/pinwheel";
import { CoundownTimer } from "@/components/ui/CountdownTimer";
import { formatTime, TGE_BONUS_ENDS, TGE_STAKE_ONE_ENDS, TGE_STAKE_THREE_ENDS, TGE_STAKE_TWO_ENDS, TGE_START } from "@/time-configs/TGE-configs";
import { ThinkProvider } from "@/components/think-provider/ThinkProvider";
import { calculateAmountFromDecimal, formatNumberWithCommas } from "@/components/ThinkTokenCard/TokenFunctions";
import { useThinkToken } from "@/components/think-provider/ThinkTokenProvider";
import ThinkDialog from "@/components/think-provider/ThinkDialog";
import { StakeDialogContent } from "@/components/think-provider/dialogs/Stake";
import { useTranslation } from "@/hooks/useTranslation";
import { ClaimStakeDialogContent } from "@/components/think-provider/dialogs/ClaimStake";
import { ClaimProvider, useClaim } from "@/components/think-provider/ClaimProvider";
import Big from "big.js";
import { RewardsProvider, useRewards } from "@/components/think-provider/RewardsProvider";

async function getTokensForAllWallets() {
    try {
        const walletResponse = await apiClient.getWallets();
        if (isGetWalletResponse(walletResponse)) {
            const walletTokens = walletResponse.wallets.flatMap(async (wallet) => {
                const tokenBalances = await web3Client.getWalletTokens(wallet.address);
                if (isGetTokenBalanceResponse(tokenBalances)) {
                    return {
                        networks: tokenBalances.allTokens,
                        wallet: {
                            address: wallet.address
                        }
                    }
                }
                return {
                    networks: [],
                    wallet: {
                        address: wallet.address
                    }
                }
            });
            return Promise.all(walletTokens);
        }
        return [];
    } catch (error) {
        console.error("❌ DASHBOARD - Error in getTokensForAllWallets:", error);
        return [];
    }
}

function DashboardContent() {
    const { user } = useAuth();
    const { t } = useTranslation();

    // Time until next season ends
    const now = new Date().getTime();
    const targetDate1 = TGE_STAKE_ONE_ENDS.getTime();
    const targetDate2 = TGE_STAKE_TWO_ENDS.getTime();
    const targetDate3 = TGE_STAKE_THREE_ENDS.getTime();
    let seasonNumber = -1;
    let nextSeasonDifference = 0;
    if (targetDate1 > now) {
        seasonNumber = 1;
        nextSeasonDifference = targetDate1 - now;
    } else if (targetDate2 > now) {
        seasonNumber = 2;
        nextSeasonDifference = targetDate2 - now;
    } else if (targetDate3 > now) {
        seasonNumber = 3;
        nextSeasonDifference = targetDate3 - now;
    }


    const [isLoading, setIsLoading] = useState(true);
    const [secondsLeft, setSecondsLeft] = useState(nextSeasonDifference / 1000);
    const [intervalId, setIntervalId] = useState<NodeJS.Timeout | undefined>();
    const [isStakeModalOpen, setIsStakeModalOpen] = useState(false);
    const [isClaimModalOpen, setIsClaimModalOpen] = useState(false);

    const { stakeDetails, stakeBookkeeperDetails, changeStakeAmount } = useStake();
    const { thinkTokenDetails } = useThinkToken();
    const { claimDetails } = useClaim();
    const { rewardsDetails } = useRewards();

    useEffect(() => {
        if (secondsLeft > 0) {
            setIntervalId(setTimeout(() => {
                setSecondsLeft(secondsLeft - 1);
            }, 1000));
        }

        return () => clearTimeout(intervalId); // Cleanup on unmount or when secondsLeft is 0
    }, [secondsLeft]);

    useEffect(() => {
        if (thinkTokenDetails?.tokenName) {
            setIsLoading(false)
        }
    }, [thinkTokenDetails?.tokenName]);

    useEffect(() => {
        if (stakeDetails.isProcessing && !isStakeModalOpen) {
            setIsStakeModalOpen(true);
        }
    }, [stakeDetails.modalStage, stakeDetails.isProcessing]);


    useEffect(() => {
        if (claimDetails.isProcessing && !isClaimModalOpen) {
            setIsClaimModalOpen(true);
        }
    }, [claimDetails.modalStage, claimDetails.isProcessing]);

    if (isLoading) {
        return (
            <DashboardLayout>
                <div className="w-full py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black ">
                    <h1 className="text-7xl text-white font-light mb-8"> Hey {user?.ensDomain || "There"},</h1>
                    <div>Loading...</div>
                </div>
            </DashboardLayout>
        )
    }

    const handleUnstakeClick = (stake: ActiveStake) => {
        stakeDetails.submitUnstake(stake.stakeId);
    };

    const handleClaimClick = () => {
        // TODO: Currently, this only takes what is claimable. We need to change
        // this to what is taken from rewards to restake (new flow that doesn't exist yet).
        claimDetails.submitClaimAndStake();
    };

    const handleStakeClick = () => {
        if (thinkTokenDetails?.balance && thinkTokenDetails?.balance.gt(0)) {
            // Set the amount to stake (use all available THINK tokens)
            changeStakeAmount(thinkTokenDetails?.balance.toNumber());

            // Check if we need to approve allowance first
            if (stakeDetails.modalStage === "initial") {
                stakeDetails.approveAllowance();
            } else if (stakeDetails.modalStage === "approved") {
                stakeDetails.submitStake();
            }
        }
    };

    const userTotalStaked = stakeBookkeeperDetails?.totalStakedAmount
        ?  formatNumberWithCommas(calculateAmountFromDecimal(BigInt(stakeBookkeeperDetails?.totalStakedAmount), BigInt(thinkTokenDetails?.decimals || 18), 2)) : "0.00";

    const globalTotalStaked = stakeBookkeeperDetails?.globalStaked
        ? calculateAmountFromDecimal(BigInt(stakeBookkeeperDetails.globalStaked), BigInt(thinkTokenDetails?.decimals || 18), 2)
        : "0.00";

    const totalSupply = thinkTokenDetails?.totalSupply 
        ? calculateAmountFromDecimal(BigInt(thinkTokenDetails?.totalSupply), BigInt(thinkTokenDetails?.decimals || 18), 2) 
        : "0.00"
        
    const userRewards = rewardsDetails?.userTotalRewards
        ? calculateAmountFromDecimal(BigInt(rewardsDetails?.userTotalRewards), BigInt(thinkTokenDetails?.decimals || 18), 2)
        : "0.00";

    const globalRewards = rewardsDetails?.globalRewards
        ? calculateAmountFromDecimal(BigInt(rewardsDetails?.globalRewards), BigInt(thinkTokenDetails?.decimals || 18), 2)
        : "0.00";

    const percentageStaked = Math.round(new Big(globalTotalStaked).div(new Big(totalSupply)).mul(100).toNumber());

    return (
        <DashboardLayout>
            <div className="w-full py-3 lg:py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black">
                <CoundownTimer
                    title="Founder's Bonus Ends In"
                    from={TGE_START}
                    to={TGE_BONUS_ENDS}
                    className="mb-4 rounded-lg"
                    action={() => alert("not implemented")}
                />

                <div className="xl:flex xl:flex-row gap-4 lg:gap-8">
                    {/* Left 2/3 area */}
                    <div className="w-full lg:flex-1 xl:w-2/3">
                        <h1 className="text-4xl md:text-6xl lg:text-7xl text-white font-light mb-6 lg:mb-8"> {user?.ensDomain ? t('pages.dashboard.greeting', { name: user.ensDomain }) : t('pages.dashboard.greetingDefault')}</h1>

                        {/* Your Rewards Box */}
                        <div className="bg-black rounded-lg p-4 xl:p-6 mb-4 xl:mb-6">
                            <h2 className="text-white text-xl lg:text-2xl font-medium mb-3 xl:mb-4">{t('pages.dashboard.yourRewards')}</h2>
                            <div className="flex flex-row  items-center gap-4">
                                <div className="flex-1">
                                    <div className="text-white text-sm font-light mb-1 ">
                                        {/* TODO: Replace with actual rewards amount */}
                                        <span className="font-[evolver-variable] text-2xl">{formatNumberWithCommas(userRewards)}</span>
                                        <span className="text-xs ml-1">THINK</span>
                                    </div>
                                    <hr className="border-gray-100 mb-1" />
                                    <div className="text-teal font-mono">{/* TODO */}&nbsp;</div>
                                </div>
                                {(new Big(userRewards).gt(0) ) ? (
                                    <GradientButton
                                        onClick={handleClaimClick}
                                    >
                                        {t('pages.dashboard.claimAndRestake')}
                                    </GradientButton>
                                ) : (
                                    <GrayGradientButtonIneligible>
                                        {t('pages.dashboard.ineligible')}
                                    </GrayGradientButtonIneligible>
                                )}
                            </div>
                        </div>

                        {/* Available to Stake Box */}
                        <div className="bg-black rounded-lg p-4 lg:p-6">
                            <h2 className="text-white text-xl lg:text-2xl font-medium mb-3 lg:mb-4">{t('pages.dashboard.availableToStake')}</h2>
                            <div className="flex flex-row sm:justify-between items-center gap-4">
                                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-end gap-4 flex-1">
                                    <div className="text-white text-sm font-light flex-1">
                                        <span className="font-[evolver-variable] text-2xl">
                                            {thinkTokenDetails?.balance ? `${formatNumberWithCommas(thinkTokenDetails?.balance.toFixed(2))}` : "0.00"}
                                        </span>
                                        <span className="text-xs ml-1">THINK</span>
                                        <hr className="border-gray-100 mb-1" />
                                        <div className="text-teal font-mono hidden">{/* TODO */}&nbsp;</div>
                                    </div>
                                </div>
                                {(thinkTokenDetails?.balance && thinkTokenDetails?.balance.gt(0)) ? (
                                    <GradientButton
                                        onClick={handleStakeClick}
                                        disabled={stakeDetails.isProcessing}
                                    >
                                        {stakeDetails.isProcessing
                                            ? "Processing..."
                                            : (thinkTokenDetails?.balance && thinkTokenDetails?.balance.gt(0) ? "Stake" : "Buy THINK")
                                        }
                                    </GradientButton>
                                )
                                    :
                                    <GrayGradientButton onClick={() => {
                                        window.open("https://x.com/thinkagents", '_blank');
                                    }}>
                                        Buy THINK
                                    </GrayGradientButton>
                                }
                            </div>
                        </div>
                    </div>

                    {/* Right 1/3 area */}
                    <div className="w-full xl:w-1/3 mt-4 xl:mt-0">
                        <div className="bg-black rounded-lg ">
                            <div className="p-4 lg:p-6">

                                <h2 className="text-white text-xl lg:text-2xl font-medium mb-4 lg:mb-6">Think Ecosystem</h2>

                                <div className="flex flex-row items-center gap-4">
                                    <div className="space-y-4 lg:space-y-6">
                                        {/* In Circulation */}
                                        <div>
                                            <div className="text-teal text-sm uppercase">Total Supply</div>
                                            <div className="text-white text-sm font-light">
                                                {/* TODO: Replace with actual circulation data */}
                                                {formatNumberWithCommas( totalSupply )}
                                            </div>
                                        </div>

                                        {/* Globally Staked */}
                                        <div>
                                            <div className="text-teal text-sm uppercase">Globally Staked</div>
                                            <div className="text-white text-sm font-light">
                                                {formatNumberWithCommas(globalTotalStaked)}
                                            </div>
                                        </div>

                                        {/* Network Rewards */}
                                        <div>
                                            <div className="text-teal text-sm uppercase">Network Rewards</div>
                                            <div className="text-white text-sm font-light">
                                                {/* TODO: Replace with actual rewards data */}
                                                {formatNumberWithCommas(globalRewards)}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex flex-grow items-center justify-center align-center gap-4">
                                        <PinWheel percentage={percentageStaked} />
                                    </div>

                                </div>
                            </div>
                            <div className="p-4 lg:px-6 border-t border-[#333]">
                                <div className="text-white text-sm font-light flex flex-row items-center gap-2">
                                    <img src="/images/knocked-out-coin.svg" className="w-auto h-6 sm:h-10" />
                                    <div className="flex-none font-bold font-mono uppercase text-teal">
                                        <div className="flex-column justify-center h-full flex items-center">Season {seasonNumber} ends <span className="ml-2 text-white">{formatTime(secondsLeft)}</span></div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div className="bg-black rounded-lg p-4 lg:p-6 mt-4">
                            <h2 className="text-white text-xl lg:text-2xl font-medium mb-4 lg:mb-6">Your Stakes</h2>
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-end gap-4 flex-1">
                                <div className="text-white text-sm font-light flex-1">
                                    <span className="font-[evolver-variable] text-2xl">
                                        {userTotalStaked}
                                    </span>
                                    <span className="text-xs ml-1">THINK</span>
                                    <hr className="border-gray-100 mb-1" />
                                    <div className="text-teal uppercase font-mono">Staked Think</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            {/* Stakes Table */}
            <div className="bg-black p-4 lg:p-6 mt-4">
                <h2 className="text-white text-xl lg:text-2xl font-medium mb-4 lg:mb-6">Your Stakes</h2>

                <div className="overflow-x-auto">
                    <table className="w-full text-left">
                        <thead>
                            <tr className="border-b border-gray-600 text-mono">
                                <th className="pb-3 text-gray-400 text-sm font-medium uppercase tracking-wider">ID</th>
                                <th className="pb-3 text-gray-400 text-sm font-medium uppercase tracking-wider">THINK Staked</th>
                                <th className="pb-3 text-gray-400 text-sm font-medium uppercase tracking-wider">Date Staked</th>
                                <th className="pb-3 text-gray-400 text-sm font-medium uppercase tracking-wider text-right">Status</th>
                            </tr>
                        </thead>
                        <tbody className="">
                            {stakeBookkeeperDetails?.activeStakes.map((stake) => {
                                const lockedUntil = stake.timestamp * 1000 + stake.timeLock * 1000;
                                const isLocked = lockedUntil > Math.floor(Date.now());
                                return (
                                    <tr key={stake.stakeId} className="hover:bg-gray-900 transition-colors">
                                        <td className="py-4 text-white">
                                            {stake.stakeId}
                                        </td>
                                        <td className="py-4 text-white font-token">
                                            <span className="">{ formatNumberWithCommas(calculateAmountFromDecimal(BigInt(stake.thinkAmount), BigInt(thinkTokenDetails?.decimals || 18), 2)) }</span>
                                        </td>
                                        <td className="py-4 text-white font-token">
                                            {new Date(stake.timestamp * 1000).toLocaleDateString('en-US', {
                                                year: 'numeric',
                                                month: 'short',
                                                day: 'numeric'
                                            })}
                                        </td>
                                        <td className="py-4 text-right">
                                            {isLocked ? (
                                                <GrayGradientButtonIneligible>
                                                    Ineligible
                                                </GrayGradientButtonIneligible>
                                            ) : (
                                                <GrayGradientButton
                                                    onClick={() => handleUnstakeClick(stake)}
                                                >
                                                    Unstake
                                                </GrayGradientButton>
                                            )
                                            }
                                        </td>
                                    </tr>
                                )
                            })}
                        </tbody>
                    </table>

                    {/* Summary row */}
                    <div className="mt-6 pt-4 border-t border-gray-600 flex justify-between items-center">
                        <div className="text-gray-400 text-sm">
                            Total Active Stakes: {stakeBookkeeperDetails?.activeStakes.length}
                        </div>
                    </div>
                </div>
            </div>
            
            <ThinkDialog
                open={isClaimModalOpen}
                onOpenChange={(isOpen) => { setIsClaimModalOpen(isOpen); }}
                {...ClaimStakeDialogContent({
                ...claimDetails,
                onClose: () => { setIsClaimModalOpen(false); },
                reset: () => { setIsClaimModalOpen(false); }
                })}
            />
            <ThinkDialog
                open={isStakeModalOpen}
                onOpenChange={(isOpen) => { setIsStakeModalOpen(isOpen); }}
                {...StakeDialogContent({
                ...stakeDetails,
                onClose: () => { setIsStakeModalOpen(false); },
                reset: () => { setIsStakeModalOpen(false); }
                })}
            />
        </DashboardLayout>
    );
}



export default function Dashboard() {
    return (
        <ThinkProvider>
            <RewardsProvider>
                <DashboardContent />
            </RewardsProvider>
        </ThinkProvider>
    );
}