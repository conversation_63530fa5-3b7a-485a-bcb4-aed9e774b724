import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { TeamMember } from "@/types";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import env from "@/environments/config";

export default function About() {
    const query = useQuery({
        queryKey: ["team-members"],
        queryFn: async () => {
            const data = await apiClient.getTeamMembers();
            return data;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });
    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">ABOUT</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">

                <h1 className="pt-4 uppercase text-4xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    Builders of the agentic web
                </h1>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    We're a group of engineers, designers, and systems thinkers with a shared mission:
                </p>

                <h2 className="pt-4 uppercase text-2xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    to allow people to think for themselves by giving them ownership over the AI they use every day.
                </h2>


                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    Our backgrounds span AI infrastructure, blockchain protocols, open-source ecosystems, and world-class product design. We’ve helped scale global platforms, ship beloved tools, and architect systems that prioritize user freedom over corporate control.
                    We’re not just building a protocol—we’re designing the foundation for a new internet.
                </p>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    One where agents are sovereign, intelligence is composable, 
                    and innovation is permissionless.
                </p>


                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    We believe the next great leap in computing is making smart AI systems that people actually own.
                </p>

                <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                    <header className="flex flex-row justify-center items-center gap-4 ">
                        <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        <h2 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">Our&nbsp;Team</h2>
                        <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    </header>
                    </div>
                    <div className="w-full mx-auto lg:px-10">
                    <div className="mt-10 md:columns-1 lg:grid lg:grid-cols-2 xl:grid-cols-3 w-full">
                        {query && query.data && query.data['team_members'] && query.data['team_members'].map((member: TeamMember) => {
                            return (
                                <div key={member.name} className="relative group px-4 lg:px-6 overflow-hidden mb-3lg:overflow-visible cursor-default">
                                    <div className="relative w-full pb-[100%] z-0">
                                        { member.image && <img src={`${env.API_BASE_URL}${member.image}`} className="absolute block w-full h-full inset-0 object-fill z-10" /> }
                                    </div>

                                    <div className="min-h-full top-0 pt-10 pb-14 overflow-hidden">
                                        <h3 className="font-token uppercase text-2xl lg:text-2xl xl:text-5xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 no-hyphen-auto" lang="en">{member.name}</h3>
                                        { member.title && <h4 className="font-mono uppercase text-white text-xs lg:text-sm font-bold mb-4">{member.title}</h4> }
                                        <div className="font-mono text-gray-400">
                                        <div className="text-sm lg:text-base mb-8">
                                            {member.bio}
                                        </div>
                                        <div className="flex flex-row">
                                            { member.url_x && 
                                                (<a href={member.url_x} className="inline-block mr-4 hover:text-sand" target="_blank">
                                                    <img src="/images/logos/x-twitter.svg" className="w-auto h-7" />
                                                </a>) 
                                            }
                                            { member.url_linkedin && 
                                                (<a href={member.url_linkedin} className="inline-block mr-4 hover:text-sand" target="_blank">
                                                    <img src="/images/logos/linked-in.svg" className="w-auto h-7" />
                                                </a>) 
                                            }
                                            { member.url_github && 
                                                (<a href={member.url_github} className="inline-block mr-4 hover:text-sand" target="_blank">
                                                    <img src="/images/logos/github.svg" className="w-auto h-7" />
                                                </a>) 
                                            }
                                            { member.url_ig && 
                                                (<a href={member.url_ig} className="inline-block mr-4 hover:text-sand" target="_blank">
                                                    <img src="/images/logos/ig.svg" className="w-auto h-7" />
                                                </a>) 
                                            }
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>

            </PrimaryContainerComponent>
            <div className="w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}