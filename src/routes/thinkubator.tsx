import React, { useRef, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import env from "@/environments/config";
import { useToast } from "@/hooks/use-toast";
import { Dialog } from "@/components/ui/dialog";
import clsx from "clsx";
import PixelTrail from '@/components/PixelTrail';

// Import SVGs for logos
import ThinkubatorLogo from "/images/logos/Thinkubator_Logo_Horizontal 1.svg";
import ApechainLogo from "/images/logos/APECHAIN-1 1.svg";
import RenderLogo from "/images/logos/Render-Network-Full.svg";
import AlchemyLogo from "/images/logos/Alchemy.svg";
import IAILogo from "/images/logos/IAI-Logo-long.svg";
import ThinkubatorIcon from "/images/logos/Thinkubator Icon.svg";
import DividerSVG from "/images/Thinkubator Divider.svg";
import KnockedOutRays from "/images/Knocked_Out_Rays_half.svg";

// Color palette
const colorRed = "#C23A27";
const colorOrange = "#E15D39";
const colorYellow = "#EDB55A";
const colorLightOrange = "#F48D43";
const colorOffWhite = "#F8F8F8";

// Utility classes for tags
const tagClass =
  "inline-block bg-yellow-700 text-black text-xs font-bold px-3 py-1 rounded-full uppercase tracking-wider shadow-sm";

// Utility classes for section headings
const sectionHeadingClass =
  "font-bold text-off-white text-2xl md:text-3xl text-center mb-4 md:mb-8 tracking-tight";

// Utility classes for card backgrounds
const cardBgClass =
  "bg-[#18181b] rounded-2xl shadow-lg border border-[#222]";

// Utility classes for input fields
const inputClass =
  "rounded px-4 py-2 bg-[#222] text-off-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-400 transition-all";

// Utility classes for buttons
const buttonClass =
  "bg-orange-500 hover:bg-orange-400 text-black font-bold py-2 px-6 rounded-full text-lg transition-colors shadow focus:outline-none focus:ring-2 focus:ring-orange-400";

// Add a custom class for Neue Haas Unica W1G
const neueHaasClass = "font-[neue-haas-unica]";

// Pills/tag utility class
const pillClass = "inline-block px-4 py-1 rounded-[4px] font-bold text-xs uppercase tracking-wider";

function SubNav({ onApplyNow, showLogo }: { onApplyNow: () => void; showLogo: boolean }) {
  // Smooth scroll handler
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>, target: string) => {
    e.preventDefault();
    const el = document.getElementById(target);
    if (el) {
      el.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <>
      {/* Divider between main nav and subnav */}
      <div className="w-full h-px bg-[#333]" />
      <nav className="thinkubator-subnav w-full flex justify-between items-center py-2 bg-black sticky top-0 z-40 border-b border-[#222] shadow-lg px-4 sm:px-6 md:px-20 xl:px-[80px] min-h-[48px]">
        {/* Left: Logo (conditionally rendered) */}
        <div className="flex items-center h-full">
          {showLogo && (
            <img
              src={ThinkubatorLogo}
              alt="Thinkubator Logo"
              className="h-6 sm:h-7 md:h-8 w-auto transition-opacity duration-700 thinkubator-logo-glow"
              style={{ opacity: showLogo ? 1 : 0 }}
              draggable={false}
            />
          )}
        </div>
        {/* Right: Nav links and button */}
        <div className="flex items-center gap-4 sm:gap-6 md:gap-8">
          <a href="#projects" onClick={e => handleSmoothScroll(e, 'projects')} className="text-off-white hover:text-[#E15D39] transition-colors font-semibold text-sm sm:text-base">Projects</a>
          <a href="#events" onClick={e => handleSmoothScroll(e, 'events')} className="text-off-white hover:text-[#E15D39] transition-colors font-semibold text-sm sm:text-base">Events</a>
          <a href="https://docs.thinkagents.ai/" target="_blank" rel="noopener noreferrer" className="text-off-white hover:text-[#E15D39] transition-colors font-semibold text-sm sm:text-base">FAQ</a>
          <button
            onClick={onApplyNow}
            className="bg-[#E15D39] hover:bg-[#F48D43] text-black font-bold py-1.5 sm:py-2 px-3 sm:px-5 text-sm sm:text-base transition-colors shadow focus:outline-none focus:ring-2 focus:ring-[#EDB55A] rounded-[5px] ml-2 sm:ml-4"
            style={{ minWidth: 0, height: '36px sm:40px' }}
          >
            Apply Now
          </button>
        </div>
      </nav>
    </>
  );
}

function HeroSection() {
  return (
    <section id="hero" className="relative w-full bg-black pt-16 sm:pt-20 md:pt-24 pb-16 sm:pb-20 md:pb-24 flex flex-col items-start justify-center min-h-[60vh]">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 md:px-20 xl:px-[80px] z-10 relative">
        <div className="w-full max-w-4xl">
          {/* Always show Thinkubator logo at the top of the hero */}
          <img
            src={ThinkubatorLogo}
            alt="Thinkubator Logo"
            className="h-6 sm:h-8 md:h-10 w-auto mb-4 sm:mb-6 thinkubator-logo-glow"
            draggable={false}
          />
          <h1
            className={clsx(
              "text-[2.5rem] sm:text-[3rem] md:text-[4rem] lg:text-[5.5rem] xl:text-[6.5rem] font-light mb-4 sm:mb-6 leading-[1.0] tracking-tight",
              neueHaasClass
            )}
            style={{ fontFamily: 'neue-haas-unica, "Neue Haas Unica W1G", Arial, sans-serif', color: colorOffWhite, fontWeight: 300, letterSpacing: '-2px', lineHeight: '1' }}
          >
            Growing the<br />tokenized app layer
          </h1>
          <div className="mb-6 sm:mb-8 font-normal tracking-tight text-[1rem] sm:text-[1.15rem]" style={{ color: colorYellow, fontWeight: 400, letterSpacing: '-1px' }}>
            Season One: <span style={{ color: colorOrange, fontWeight: 400, letterSpacing: '-1px' }}>Summer 2025</span>
          </div>
          <div className="flex flex-row gap-3 sm:gap-4 md:gap-6 items-center mb-8 sm:mb-10 flex-wrap">
            <img src={ApechainLogo} alt="Apechain" className="bg-black rounded" style={{ maxHeight: 25, width: 'auto' }} />
            <img src={RenderLogo} alt="Render Network" style={{ maxHeight: 25, width: 'auto' }} />
            <img src={AlchemyLogo} alt="Alchemy" style={{ maxHeight: 25, width: 'auto' }} />
            <img src={IAILogo} alt="IAI" style={{ maxHeight: 25, width: 'auto' }} />
          </div>
        </div>
      </div>
      {/* Retro lines/graphic divider full-bleed */}
      <div className="absolute left-0 right-0 bottom-0 w-screen z-0" style={{ minHeight: '14vw' }}>
        <img src={DividerSVG} alt="Divider" className="w-full h-full object-contain" style={{ minHeight: '14vw' }} />
      </div>
    </section>
  );
}

// ProjectCard component
function ProjectCard({ type, name, companies }: { type: string; name: string; companies: { src: string; alt: string; className?: string }[] }) {
  return (
    <div className="season-gradient-card flex justify-center items-center h-full min-h-[180px] px-2 sm:px-4 border-2 border-[#444] rounded-md transition-shadow shadow-none relative overflow-visible bg-transparent w-full"
      tabIndex={0}
      role="button"
      aria-label={name}
    >
      <div className="flex flex-col items-center w-full gap-y-4">
        <div className="text-[0.6rem] sm:text-[0.7rem] uppercase text-gray-400 font-mono tracking-widest text-center w-full">{type}</div>
        <div className="text-base sm:text-lg font-bold text-center" style={{ color: colorYellow }}>{name}</div>
        <div className="flex flex-row gap-3 justify-center w-full flex-wrap">
          {companies.map((company, idx) => (
            <img key={idx} src={company.src} alt={company.alt} className={`h-7 w-auto mx-auto ${company.className || ""}`} />
          ))}
        </div>
      </div>
    </div>
  );
}

function ProjectsSection() {
  const projects = [
    {
      type: "Personal Finance Vault",
      name: "[redacted]",
      companies: [
        { src: "/images/logos/cash-app-white.svg", alt: "Cash App" },
        { src: "/images/logos/square-white.svg", alt: "Square" },
        { src: "/images/logos/apple-white.svg", alt: "Apple" },
      ],
    },
    {
      type: "Agent Gaming",
      name: "[redacted]",
      companies: [
        { src: "/images/logos/Command-Line-White.svg", alt: "Command Line" },
        { src: "/images/logos/EpicGames.svg", alt: "Epic Games" },
        { src: "/images/logos/FortniteLogo-White.svg", alt: "Fortnite" },
      ],
    },
    {
      type: "Generative Image & Video Tools",
      name: "Augmented Imagination",
      companies: [
        { src: "/images/logos/Fellowship-white.svg", alt: "Fellowship", className: "max-w-[75px]" },
        { src: "/images/logos/objkt-logo-white.svg", alt: "objkt", className: "max-w-[75px]" },
        { src: "/images/logos/transient-labs-white.svg", alt: "Transient Labs", className: "max-w-[75px]" },
      ],
    },
  ];
  return (
    <section id="projects" className="w-full bg-black border-b border-[#333] py-12 sm:py-16 flex flex-col items-start max-w-7xl mx-auto px-2 sm:px-4 md:px-8 xl:px-[80px]">
      <div className="mb-3">
        <span className={pillClass} style={{ background: colorYellow, color: '#222', borderRadius: 4, letterSpacing: '0.08em', fontFamily: 'IBM Plex Mono, monospace' }}>Season One Cohort</span>
      </div>
      <div className="text-gray-300 mb-6 sm:mb-7 max-w-2xl text-left font-mono text-[0.9rem] sm:text-[1rem]" style={{ color: colorOffWhite }}>
        This is your front-row seat to the first batch of cohort projects and partnerships. Our first cohort will expand what is possible for THINK agents. Testing the edges. Setting the standard. Keep up with the projects in the Season 1 cohort below.
      </div>
      <section className="mt-8 w-full">
        <div className="w-full">
          <div className="flex flex-row flex-wrap justify-center gap-6">
            {projects.map((project, idx) => (
              <div key={idx} className="flex-1 min-w-[320px] max-w-[420px] mx-auto">
                <ProjectCard {...project} />
              </div>
            ))}
          </div>
        </div>
      </section>
    </section>
  );
}

function AboutSection() {
  return (
    <section
      id="about"
      className="w-screen flex flex-col items-center justify-center min-h-[60vh] py-12 sm:py-16 md:py-20 bg-black"
      style={{ position: "relative", overflow: "hidden", left: 0, right: 0, width: "100vw" }}
    >
      {/* Pixel Trail Background */}
      <div
        style={{
          position: "absolute",
          inset: 0,
          width: "100vw",
          height: "100%",
          zIndex: 0,
          pointerEvents: "none",
        }}
      >
        <PixelTrail
          gridSize={50}
          trailSize={0.25}
          maxAge={200}
          interpolate={0.05}
          color="#d97568"
          gooeyFilter={{ id: "custom-goo-filter", strength: 2 }}
        />
      </div>
      {/* Content */}
      <div
        style={{
          position: "relative",
          zIndex: 1,
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          pointerEvents: "none",
          padding: "0 1rem",
        }}
      >
        <img
          src={ThinkubatorLogo}
          alt="Thinkubator Logo"
          className="mb-4 sm:mb-6"
          style={{ maxWidth: 280, width: "100%", height: "auto", pointerEvents: "auto" }}
        />
        <p
          className="text-gray-300 text-center max-w-2xl font-mono mb-6 sm:mb-8 text-sm sm:text-base px-4"
          style={{ color: colorOffWhite, pointerEvents: "auto" }}
        >
          We grow Tokenized Applications, or App Coins for short, to gamify open source contribution and community expansion. Successful projects will aim to get their first 1000 users during this season. The best software of the intelligence age can be owned by us.
        </p>
        <a
          href="https://docs.thinkagents.ai/"
          target="_blank"
          rel="noopener noreferrer"
          className="bg-[#E15D39] hover:bg-[#F48D43] text-black font-bold py-2 px-4 sm:px-6 rounded-full text-base sm:text-lg transition-colors shadow focus:outline-none focus:ring-2 focus:ring-[#EDB55A]"
          style={{ display: "inline-block", pointerEvents: "auto" }}
        >
          Read Thinkubator FAQ
        </a>
      </div>
    </section>
  );
}

// Update EventsSection to include yellow tag
function EventsSection() {
  return (
    <section id="events" className="relative w-full flex items-center" style={{ minHeight: '50vh' }}>
      <div className="absolute inset-0 w-full h-full bg-[#C23A27] z-0" aria-hidden="true" />
      <div className="relative flex flex-col sm:flex-col md:flex-row items-center md:items-stretch max-w-7xl mx-auto px-4 sm:px-6 md:px-20 xl:px-[80px] gap-8 sm:gap-10 md:gap-16 z-10 w-full py-12 sm:py-16">
        <div className="flex-shrink-0 w-full md:w-[420px] flex items-center justify-center">
          <img src="/images/V02_THINK_OPEN_SOURCERERS_Graphic.webp" alt="Open Sourcerers" className="w-full h-auto rounded-md object-contain bg-black" style={{ maxHeight: 280, maxWidth: '100%' }} />
        </div>
        <div className="flex flex-col justify-center items-start w-full max-w-xl">
          <span className={pillClass + " mb-4"} style={{ background: colorYellow, color: '#222', borderRadius: 4 }}>Events</span>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2 text-off-white">Open Sourcerers</h2>
          <div className="mb-6 font-mono text-sm sm:text-base">Weekly builders group for building on THINK. Meet Thursdays at noon Pacific.</div>
          <a
            href="https://discord.gg/TD7Sbv2A"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-[#0E0F12] hover:bg-[#F48D43] text-white hover:text-black font-bold py-2 px-4 sm:px-6 rounded-full text-base sm:text-lg transition-colors duration-200 shadow focus:outline-none focus:ring-2 focus:ring-[#EDB55A] flex items-center gap-2"
          >
            {/* Discord Logo SVG */}
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ display: 'inline', verticalAlign: 'middle' }}>
              <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276c-.598.3428-1.2205.6447-1.8733.8923a.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1835 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1835 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" fill="currentColor"/>
            </svg>
            Join us on Discord
          </a>
        </div>
      </div>
    </section>
  );
}

// Update SeasonTwoSection to use a two-column flex layout. Place all text, info, and the Apply Now button on the left. Add the Knocked_Out_Rays_half.svg as an image on the right, filling the height and aligned to the right. Ensure the section remains responsive and visually balanced.
function SeasonTwoSection({ onApplyNow }: { onApplyNow?: () => void }) {
  return (
    <section
      id="season-two"
      className="relative flex items-center justify-start bg-black overflow-visible"
      style={{ minHeight: '100vh', width: '100vw', position: 'relative', padding: 0 }}
    >
      {/* Left: Content */}
      <div className="flex flex-col justify-center items-start z-10 px-4 md:px-20 xl:px-[120px] max-w-7xl mx-auto w-full">
        <div
          style={{
            background: '#0E0F12',
            borderRadius: '12px',
            padding: '2rem',
            maxWidth: 600,
            width: '100%',
            boxSizing: 'border-box',
          }}
        >
          <div className="mb-4">
            <span className={pillClass} style={{ background: colorYellow, color: '#222', borderRadius: 4 }}>Season Two Cohort</span>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-off-white text-left mb-4">Fall 2025 — Begins Early October</h2>
          <p className="text-gray-300 text-left max-w-2xl mb-4 font-mono" style={{ color: colorOffWhite }}>
            Interested in joining the next season? This is your chance to go from idea to reality with the support of THINK's ecosystem. We're looking for founders, hackers, and dreamers who are building the future of intelligence. We want to hear from you!
          </p>
          <div className="flex flex-col gap-2 mb-6">
            <div className="text-sm text-gray-400 font-mono flex items-center gap-2">
              <span role="img" aria-label="clock">🕒</span> Applications due Jul 31, 2025
            </div>
            <div className="text-sm text-gray-400 font-mono flex items-center gap-2">
              <span role="img" aria-label="thumbs up">👍</span> Teams selected early September
            </div>
          </div>
          <button
            className="bg-[#E15D39] hover:bg-[#F48D43] text-black font-bold py-2 px-6 rounded-full text-lg transition-colors shadow focus:outline-none focus:ring-2 focus:ring-[#EDB55A] mb-8"
            onClick={(onApplyNow)}
          >
            Apply Now
          </button>
        </div>
      </div>
      {/* Right: SVG Rays - absolutely positioned at far right, not cut off */}
      <img
        src={KnockedOutRays}
        alt="Decorative Rays"
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
          height: '100vh',
          width: '200vh',
          maxWidth: 'none',
          zIndex: 1,
          pointerEvents: 'none',
          objectFit: 'cover',
          transform: 'none',
        }}
      />
    </section>
  );
}

export default function Thinkubator() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [modalOpen, setModalOpen] = useState(false);
  const [showNavLogo, setShowNavLogo] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Get the hero section's bottom position
      const hero = document.getElementById('hero');
      if (hero) {
        const rect = hero.getBoundingClientRect();
        // If the bottom of the hero is above the top of the viewport, show the nav logo
        setShowNavLogo(rect.bottom <= 0);
      }
    };
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Run on mount
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!env.THINKUBATOR_ENABLED) {
    toast({
      variant: "destructive",
      description: "Thinkubator is not enabled",
    });
    navigate("/");
  }
  return (
    <div className="bg-black min-h-screen w-full">
      <SubNav onApplyNow={() => window.open("https://docs.google.com/forms/d/e/1FAIpQLSdvD84zRnsy5YsKlCpQNsArGQxV4vusDuyj3weCgd7jANxeuQ/viewform", '_blank')} showLogo={showNavLogo} />
      <HeroSection />
      <ProjectsSection />
      <EventsSection />
      <SeasonTwoSection onApplyNow={() => window.open("https://docs.google.com/forms/d/e/1FAIpQLSdvD84zRnsy5YsKlCpQNsArGQxV4vusDuyj3weCgd7jANxeuQ/viewform", '_blank')} />
      <AboutSection />
    </div>
  );
}
