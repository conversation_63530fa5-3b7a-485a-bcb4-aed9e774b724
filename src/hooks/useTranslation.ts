import { useTranslation as useI18nTranslation } from 'react-i18next';

/**
 * Custom hook that wraps react-i18next's useTranslation hook
 * Provides type-safe access to translations with better developer experience
 */
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  return {
    t,
    i18n,
    // Helper functions for common translation patterns
    changeLanguage: (lng: string) => i18n.changeLanguage(lng),
    currentLanguage: i18n.language,
    isLoading: !i18n.isInitialized,
  };
};

// Type-safe translation keys for better IDE support
export type TranslationKey = 
  | 'header.navigation.about'
  | 'header.navigation.thinkProtocol'
  | 'header.navigation.resources'
  | 'header.navigation.contact'
  | 'header.dropdown.about.team'
  | 'header.dropdown.about.partners'
  | 'header.dropdown.about.investors'
  | 'header.dropdown.thinkProtocol.thinkToken'
  | 'header.dropdown.thinkProtocol.thinkBuilders'
  | 'header.dropdown.resources.docs'
  | 'header.dropdown.resources.whitepaper'
  | 'header.dropdown.resources.faqs'
  | 'header.buttons.mintAnnouncement'
  | 'header.buttons.joinTheMovement'
  | 'header.buttons.claimThink'
  | 'header.countdown.foundersBonus'
  | 'header.footer.privacy'
  | 'header.footer.terms'
  | 'header.accessibility.openMainMenu'
  | 'header.accessibility.closeMenu'
  | 'header.socialMedia.twitter'
  | 'header.socialMedia.discord'
  | 'header.socialMedia.youtube'
  | 'header.socialMedia.magicEden';

export default useTranslation;
