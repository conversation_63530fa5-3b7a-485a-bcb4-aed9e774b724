import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import enTranslations from '../locales/en.json';
import esTranslations from '../locales/es.json';

const resources = {
  en: {
    translation: enTranslations,
  },
  es: {
    translation: esTranslations,
  },
  // Add more languages here as needed
  // fr: {
  //   translation: frTranslations,
  // },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    // Optional: Add namespace support
    defaultNS: 'translation',
    
    // Optional: Add debugging in development
    debug: import.meta.env.DEV,
    
    // Optional: Add key separator and namespace separator
    keySeparator: '.',
    nsSeparator: ':',
  });

export default i18n;
