import { getBearerToken, logoutAndClearStorage, useAuth } from "@/components/auth/AuthContext";
import env from "@/environments/config";
import { ActiveClaimEntriesResponse, ClaimsTransaction, CreateClaimsTransactionResponse, EligibleSnapshotsResponse, GetNFTResponse, GetTokenBalanceResponse, GetWalletResponse, InvestorsResponse, LogoImage, PartnersResponse, PollTransactionResponse, WallerRetrieveData } from "@/types";

const BASE_URL = `${env.API_BASE_URL}/api`;
const WEB3_BASE_URL = `${env.WEB3_API_BASE_URL}/web3`;

const fetcher = async <T>({
    url,
    method,
    body,
    headers,
    fetchType = "api"
}: {
    url: string;
    method?: "GET" | "POST";
    body?: object | FormData;
    headers?: HeadersInit;
    fetchType?: "web3" | "api",
}) => {
    const options: RequestInit = {
        method: method ?? "GET",
        headers: headers
            ? headers
            : {
                Accept: "application/json",
                "Content-Type": "application/json",
            },
    };

    if (method === "POST") {
        if (body instanceof FormData) {
            // @ts-expect-error - Supressing potentially undefined options header
            delete options.headers["Content-Type"];
            options.body = body;
        } else {
            options.body = JSON.stringify(body);
        }
    }

    let base_url = BASE_URL;
    if (fetchType === "web3") {
        base_url = WEB3_BASE_URL;
    }

    return fetch(`${base_url}${url}`, options).then(async (resp) => {
        // console.log("resp", resp);
        if (resp.status === 401) {

            throw new Error("Unauthorized. Logging out.");
        }
        if (resp.ok) {
            const contentType = resp.headers.get("Content-Type");

            if (contentType === "audio/mpeg") {
                return await resp.blob();
            }
            return resp.json() as T;
        }
        if (resp.status === 404) {
            return resp.json() as T;
        }

        const errorText = await resp.text();
        let errorJson = null;
        try {
            errorJson = await JSON.parse(errorText);
        } catch (e) {
            console.error(e);
        }

        if (resp.status >= 400 && !errorJson) {
            throw new Error(errorText);
        }
        console.error("Error: ", errorText);

        let errorMessage = "An error occurred.";
        try {
            const errorObj = JSON.parse(errorText);
            errorMessage = errorObj.message || errorMessage;
        } catch {
            errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
    });
};

export const web3Client = {
    retrieveWalletData: (address: string) =>
        fetcher<any>({
            fetchType: "web3",
            url: `/wallets/${address}/retrieve`,
            method: "POST",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getWalletNFTs: (address: string) =>
        fetcher<GetNFTResponse>({
            fetchType: "web3",
            url: `/wallets/${address}/nfts`,
            method: "GET",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getWalletTokens: (address: string) =>
        fetcher<GetTokenBalanceResponse>({
            fetchType: "web3",
            // TODO change network to dynamic per wallet
            url: `/wallets/${address}/tokens`,
            method: "GET",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getWalletEligibility: (address: string) =>
        fetcher<EligibleSnapshotsResponse>({
            fetchType: "web3",
            // TODO change network to dynamic per wallet
            url: `/wallets/${address}/eligibility`,
            method: "GET",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    createClaimsTransaction: (address: string, claimableIds: number[]) =>
        fetcher<ClaimsTransaction>({
            fetchType: "web3",
            // TODO change network to dynamic per wallet
            url: `/wallets/${address}/claimEligibility`,
            method: "POST",
            body: { claimableIds },
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    createPollTransaction: (address: string, claimId: string, transactionHash: string) =>
        fetcher<PollTransactionResponse>({
            fetchType: "web3",
            // TODO change network to dynamic per wallet
            url: `/wallets/${address}/pollClaimTransaction`,
            method: "POST",
            body: { claimId, transactionHash },
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getActiveClaims: (address: string) =>
        fetcher<ActiveClaimEntriesResponse>({
            fetchType: "web3",
            // TODO change network to dynamic per wallet
            url: `/wallets/${address}/activeClaims`,
            method: "GET",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
};

export const apiClient = {
    authenticateWallet: (address: string) =>
        fetcher<any>({
            url: "/wallets",
            method: "POST",
            body: { address },
        }),
    addWallet: (address: string) =>
        fetcher<any>({
            url: "/wallets/add",
            method: "POST",
            body: { address },
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    removeWallet: (walletId: string) =>
        fetcher<any>({
            url: `/wallets/${walletId}/remove`,
            method: "POST",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    setPrimaryWallet: (walletId: string) =>
        fetcher<any>({
            url: `/wallets/${walletId}/set_primary`,
            method: "POST",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getWallets: (): Promise<GetWalletResponse | Blob> =>
        fetcher<GetWalletResponse>({
            url: "/wallets",
            method: "GET",
            headers: {
                Authorization: `Bearer ${getBearerToken()}`,
            },
        }),
    getTeamMembers: () =>
        fetcher<any>({
            url: "/team_members",
            method: "GET"
        }),
    getInvestors: () =>
        fetcher<InvestorsResponse>({
            url: "/investors",
            method: "GET"
        }),
    getPartners: () =>
        fetcher<PartnersResponse>({
            url: "/partners",
            method: "GET"
        }),
    getBlogPosts: (limit: number = 9, offset: number = 0) =>
        fetcher<any>({
            url: `/posts?limit=${limit}&offset=${offset}`,
            method: "GET"
        }),
    getBlogPost: (postSlug: string) =>
        fetcher<any>({
            url: `/posts/${postSlug}`,
            method: "GET"
        }),
    getFAQ: () =>
        fetcher<any>({
            url: "/faq",
            method: "GET"
        }),
    submitContact: (formData: any) =>
        fetcher<any>({
            url: "/contacts",
            method: "POST",
            body: formData,
        }),
};

export function isGetWalletResponse(obj: any): obj is GetWalletResponse {
    return 'wallets' in obj;
}

export function isGetInvestorsResponse(obj: any): obj is InvestorsResponse {
    return 'investors' in obj;
}

export function isGetPartnersResponse(obj: any): obj is PartnersResponse {
    return 'partners' in obj;
}

export function isGetNFTResponse(obj: any): obj is GetNFTResponse {
    return 'allNfts' in obj;
}

export function isGetTokenBalanceResponse(obj: any): obj is GetTokenBalanceResponse {
    return 'allTokens' in obj;
}

export function isEligibleSnapshotsResponse(obj: any): obj is EligibleSnapshotsResponse {
    return 'claimables' in obj;
}

export function isClaimsTransaction(obj: any): obj is ClaimsTransaction {
    return 'claimPayload' in obj;
}

export function isActiveClaimsTransaction(obj: any): obj is ActiveClaimEntriesResponse {
    return 'activeClaims' in obj;
}


